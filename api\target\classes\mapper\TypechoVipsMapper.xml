<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoVipsDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoVips" >
        <result column="id" property="id" />
        <result column="orderKey" property="orderKey" />
        <result column="name" property="name" />
        <result column="price" property="price" />
        <result column="day" property="day" />
        <result column="giftDay" property="giftDay" />
        <result column="intro" property="intro" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `orderKey`,
        `name`,
        `price`,
        `day`,
        `giftDay`,
        `intro`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoVips">
        INSERT INTO ${prefix}_vips
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != orderKey'>
                `orderKey`,
            </if>
            <if test ='null != name'>
                `name`,
            </if>
            <if test ='null != price'>
                `price`,
            </if>
            <if test ='null != day'>
                `day`,
            </if>
            <if test ='null != giftDay'>
                `giftDay`,
            </if>
            <if test ='null != intro'>
                `intro`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != orderKey'>
                #{orderKey},
            </if>
            <if test ='null != name'>
                #{name},
            </if>
            <if test ='null != price'>
                #{price},
            </if>
            <if test ='null != day'>
                #{day},
            </if>
            <if test ='null != giftDay'>
                #{giftDay},
            </if>
            <if test ='null != intro'>
                #{intro}
            </if>
        </trim>
    </insert>

    <!-- 批量插入数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${prefix}_vips ( <include refid="Base_Column_List" /> ) VALUES
        <foreach collection="list" item="curr" index="index" separator=",">
            (
                #{curr.id},
                #{curr.orderKey},
                #{curr.name},
                #{curr.price},
                #{curr.day},
                #{curr.giftDay},
                #{curr.intro}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoVips">
        UPDATE ${prefix}_vips
        <set>
            <if test ='null != orderKey'>`orderKey` = #{orderKey},</if>
            <if test ='null != name'>`name` = #{name},</if>
            <if test ='null != price'>`price` = #{price},</if>
            <if test ='null != day'>`day` = #{day},</if>
            <if test ='null != giftDay'>`giftDay` = #{giftDay},</if>
            <if test ='null != intro'>`intro` = #{intro}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_vips
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_vips WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_vips
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_vips
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != orderKey'>
                and `orderKey` = #{orderKey}
            </if>
            <if test ='null != name'>
                and `name` = #{name}
            </if>
            <if test ='null != price'>
                and `price` = #{price}
            </if>
            <if test ='null != day'>
                and `day` = #{day}
            </if>
            <if test ='null != giftDay'>
                and `giftDay` = #{giftDay}
            </if>
            <if test ='null != intro'>
                and `intro` = #{intro}
            </if>
        </where>
        order by orderKey desc
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_vips
        <where>
            <if test ='null != typechoVips.id'>
                and `id` = #{typechoVips.id}
            </if>
            <if test ='null != typechoVips.orderKey'>
                and `orderKey` = #{typechoVips.orderKey}
            </if>
            <if test ='null != typechoVips.name'>
                and `name` = #{typechoVips.name}
            </if>
            <if test ='null != typechoVips.price'>
                and `price` = #{typechoVips.price}
            </if>
            <if test ='null != typechoVips.day'>
                and `day` = #{typechoVips.day}
            </if>
            <if test ='null != typechoVips.giftDay'>
                and `giftDay` = #{typechoVips.giftDay}
            </if>
            <if test ='null != typechoVips.intro'>
                and `intro` = #{typechoVips.intro}
            </if>
        </where>
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_vips
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != orderKey'>
                and `orderKey` = #{orderKey}
            </if>
            <if test ='null != name'>
                and `name` = #{name}
            </if>
            <if test ='null != price'>
                and `price` = #{price}
            </if>
            <if test ='null != day'>
                and `day` = #{day}
            </if>
            <if test ='null != giftDay'>
                and `giftDay` = #{giftDay}
            </if>
            <if test ='null != intro'>
                and `intro` = #{intro}
            </if>
        </where>
    </select>
</mapper>